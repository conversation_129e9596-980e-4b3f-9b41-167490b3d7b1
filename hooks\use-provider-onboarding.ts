"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createClient } from "@/lib/supabase/client";
import { toast } from "sonner";
import { authKeys } from "@/hooks/use-auth";
import type { ProviderOnboardingFormData } from '@/types/form.types';

// Types - use consolidated type from form.types.ts
export type ProviderOnboardingData = ProviderOnboardingFormData;

export interface OnboardingProgress {
  step: number;
  completed: boolean;
  data?: Partial<ProviderOnboardingData>;
}

export interface CreateProviderResponse {
  success: boolean;
  providerId?: string;
  message?: string;
}

// Query Keys
export const providerOnboardingKeys = {
  all: ['provider-onboarding'] as const,
  status: () => [...providerOnboardingKeys.all, 'status'] as const,
  progress: () => [...providerOnboardingKeys.all, 'progress'] as const,
} as const;

// Helper function to safely check if value is a File instance
const isFileInstance = (value: any): value is File => {
  return typeof File !== "undefined" && value instanceof File;
};

// File upload function
export async function uploadFile(
  file: File,
  bucket: string,
  path: string
): Promise<string> {
  const supabase = createClient();
  
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: false
    });

  if (error) {
    throw new Error(`Failed to upload file: ${error.message}`);
  }

  // Get public URL
  const { data: urlData } = supabase.storage
    .from(bucket)
    .getPublicUrl(data.path);

  return urlData.publicUrl;
}

// API Functions
export async function createCateringProvider(
  data: ProviderOnboardingData
): Promise<CreateProviderResponse> {
  const supabase = createClient();
  
  // Get current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    throw new Error("User not authenticated");
  }

  let logoUrl: string | null = null;
  let sampleMenuUrl: string | null = null;

  try {
    // Upload logo if provided
    if (isFileInstance(data.logo)) {
      const logoPath = `logos/${user.id}/${Date.now()}-${data.logo.name}`;
      logoUrl = await uploadFile(data.logo, 'provider-assets', logoPath);
    }

    // Upload sample menu if provided
    if (isFileInstance(data.sampleMenu)) {
      const menuPath = `menus/${user.id}/${Date.now()}-${data.sampleMenu.name}`;
      sampleMenuUrl = await uploadFile(data.sampleMenu, 'provider-assets', menuPath);
    }

    // Insert catering provider data
    const { data: providerData, error: insertError } = await supabase
      .from('catering_providers')
      .insert({
        user_id: user.id,
        business_name: data.businessName,
        business_address: data.businessAddress || null,
        logo_url: logoUrl,
        description: data.description,
        service_areas: data.serviceAreas,
        sample_menu_url: sampleMenuUrl,
        contact_person_name: data.contactPersonName,
        mobile_number: data.mobileNumber,
        social_media_links: data.socialMediaLinks || {},
        onboarding_completed: true,
        onboarding_step: 3,
      })
      .select('id')
      .single();

    if (insertError) {
      throw new Error(`Failed to create provider profile: ${insertError.message}`);
    }

    // Update user role to catering_provider
    const { error: roleError } = await supabase
      .from('user_roles')
      .insert({
        user_id: user.id,
        role: 'catering_provider',
        provider_role: 'owner'
      });

    if (roleError) {
      // If role already exists, update it
      const { error: updateRoleError } = await supabase
        .from('user_roles')
        .update({
          role: 'catering_provider',
          provider_role: 'owner'
        })
        .eq('user_id', user.id);

      if (updateRoleError) {
        throw new Error(`Failed to update user role: ${updateRoleError.message}`);
      }
    }

    return {
      success: true,
      providerId: providerData.id,
      message: "Provider profile created successfully"
    };

  } catch (error) {
    // Clean up uploaded files if provider creation fails
    if (logoUrl) {
      try {
        const logoPath = logoUrl.split('/').pop();
        if (logoPath) {
          await supabase.storage
            .from('provider-assets')
            .remove([`logos/${user.id}/${logoPath}`]);
        }
      } catch (cleanupError) {
        console.warn('Failed to cleanup logo file:', cleanupError);
      }
    }

    if (sampleMenuUrl) {
      try {
        const menuPath = sampleMenuUrl.split('/').pop();
        if (menuPath) {
          await supabase.storage
            .from('provider-assets')
            .remove([`menus/${user.id}/${menuPath}`]);
        }
      } catch (cleanupError) {
        console.warn('Failed to cleanup menu file:', cleanupError);
      }
    }

    throw error;
  }
}

export async function checkExistingProvider(): Promise<boolean> {
  const supabase = createClient();
  
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    return false;
  }

  const { data, error } = await supabase
    .from('catering_providers')
    .select('id')
    .eq('user_id', user.id)
    .single();

  return !error && !!data;
}

export async function getOnboardingProgress(): Promise<OnboardingProgress> {
  const supabase = createClient();
  
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    throw new Error("User not authenticated");
  }

  const { data, error } = await supabase
    .from('catering_providers')
    .select('*')
    .eq('user_id', user.id)
    .single();

  if (error) {
    // No existing provider data
    return { step: 1, completed: false };
  }

  return {
    step: data.onboarding_step || 1,
    completed: data.onboarding_completed || false,
    data: {
      businessName: data.business_name,
      businessAddress: data.business_address,
      description: data.description,
      serviceAreas: data.service_areas,
      contactPersonName: data.contact_person_name,
      mobileNumber: data.mobile_number,
      socialMediaLinks: data.social_media_links,
    }
  };
}

// Custom Hooks

/**
 * Hook to create a catering provider profile with optimistic updates
 */
export function useCreateProvider() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createCateringProvider,
    onMutate: async () => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: providerOnboardingKeys.status() });
      
      // Snapshot the previous value
      const previousStatus = queryClient.getQueryData(providerOnboardingKeys.status());
      
      // Optimistically update to show provider status as true
      queryClient.setQueryData(providerOnboardingKeys.status(), true);
      
      return { previousStatus };
    },
    onError: (error, _variables, context) => {
      // Rollback on error
      if (context?.previousStatus !== undefined) {
        queryClient.setQueryData(providerOnboardingKeys.status(), context.previousStatus);
      }
      
      // Show error toast
      toast.error(
        error instanceof Error 
          ? error.message 
          : "Failed to create provider profile. Please try again."
      );
    },
    onSuccess: () => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: authKeys.user });
      queryClient.invalidateQueries({ queryKey: ["userRole"] });
      queryClient.invalidateQueries({ queryKey: providerOnboardingKeys.all });

      // Show success toast
      toast.success("Onboarding completed successfully! Welcome to CateringHub!");
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: providerOnboardingKeys.status() });
    },
    retry: (failureCount, error) => {
      // Don't retry validation errors or authentication errors
      if (error.message.includes('validation') ||
          error.message.includes('authentication') ||
          error.message.includes('already exists')) {
        return false;
      }

      // Retry network errors up to 3 times
      if (error.message.includes('network') ||
          error.message.includes('fetch') ||
          error.message.includes('timeout')) {
        return failureCount < 3;
      }

      // Retry other errors once
      return failureCount < 1;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Hook to check if user is already a provider
 */
export function useProviderStatus() {
  return useQuery({
    queryKey: providerOnboardingKeys.status(),
    queryFn: checkExistingProvider,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry authentication errors
      if (error.message.includes('authentication')) {
        return false;
      }
      // Retry network errors up to 2 times
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get onboarding progress
 */
export function useOnboardingProgress() {
  return useQuery({
    queryKey: providerOnboardingKeys.progress(),
    queryFn: getOnboardingProgress,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry authentication errors
      if (error.message.includes('authentication')) {
        return false;
      }
      // Retry network errors up to 2 times
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    refetchOnWindowFocus: false,
  });
}
