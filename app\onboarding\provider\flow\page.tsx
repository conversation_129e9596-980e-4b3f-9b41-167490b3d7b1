"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON>Left, ChefHat } from "lucide-react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { LoadingState } from "@/components/ui/loading-state";
import {
  Save,
  RotateCcw,
  Trash2,
  RefreshCw,
  AlertTriangle,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  MultiStepForm,
  useMultiStepForm,
} from "@/components/ui/multi-step-form";
// Lazy load step components for better performance
const BusinessInfoStep = React.lazy(() =>
  import("@/components/onboarding/business-info-step").then((module) => ({
    default: module.BusinessInfoStep,
  }))
);
const ServiceDetailsStep = React.lazy(() =>
  import("@/components/onboarding/service-details-step").then((module) => ({
    default: module.ServiceDetailsStep,
  }))
);
const ContactInfoStep = React.lazy(() =>
  import("@/components/onboarding/contact-info-step").then((module) => ({
    default: module.ContactInfoStep,
  }))
);
import { OnboardingErrorBoundary } from "@/components/onboarding/onboarding-error-boundary";
import { StepErrorBoundary } from "@/components/onboarding/step-error-boundary";
import {
  SuccessMessage,
  StepCompletionAnimation,
} from "@/components/ui/success-animation";
import { useUser, useIsProvider } from "@/hooks/use-auth";
import {
  useCreateProvider,
  useProviderStatus,
  type ProviderOnboardingData,
} from "@/hooks/use-provider-onboarding";
import { useOnboardingForm } from "@/hooks/use-onboarding-form";

// Use the type from the hooks file
type OnboardingFormData = ProviderOnboardingData;

const steps = [
  {
    id: "business-info",
    title: "Business Information",
    description: "Tell us about your catering business",
  },
  {
    id: "service-details",
    title: "Service Details",
    description: "Describe your services and coverage area",
  },
  {
    id: "contact-info",
    title: "Contact Information",
    description: "How customers can reach you",
  },
];

export default function ProviderOnboardingFlowPage() {
  const router = useRouter();
  const { data: user, isLoading: isUserLoading } = useUser();
  const { value: isProvider } = useIsProvider();

  // TanStack Query hooks
  const createProviderMutation = useCreateProvider();
  const { data: isExistingProvider, isLoading: isCheckingProvider } =
    useProviderStatus();

  // Unified form hook
  const onboardingForm = useOnboardingForm({
    enableAutoSave: true,
  });

  // Multi-step form state with optimized navigation
  const { currentStep, nextStep, previousStep, canGoPrevious, goToStep } =
    useMultiStepForm({
      totalSteps: steps.length,
      onStepChange: (_step: number) => {
        // Auto-save when navigating between steps
        onboardingForm.saveToStorage();
      },
    });

  // Use unified form validation instead of manual step validation
  const stepValidation: Record<number, boolean> = React.useMemo(() => {
    return {
      1: onboardingForm.isStepValid(1), // Business Info
      2: onboardingForm.isStepValid(2), // Service Details
      3: onboardingForm.isStepValid(3), // Contact Info
    };
  }, [onboardingForm.formState.errors]);

  // Storage status (memoized to prevent unnecessary re-renders)
  const storageInfo = React.useMemo(
    () => onboardingForm.getStorageInfo(),
    [onboardingForm]
  );

  // Success animation state
  const [showSuccessMessage, setShowSuccessMessage] = React.useState(false);
  const [completedStepAnimation, setCompletedStepAnimation] = React.useState<
    number | null
  >(null);

  // Navigation state to prevent unnecessary re-renders
  const [isRedirecting, setIsRedirecting] = React.useState(false);
  const redirectTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Manual storage operations (commented out as unused)
  // const handleManualSave = () => {
  //   onboardingForm.saveToStorage();
  //   toast.success("Form data saved successfully!");
  // };

  const handleLoadFromStorage = React.useCallback(() => {
    onboardingForm.loadFromStorage();
    toast.success("Form data loaded from storage!");
  }, [onboardingForm]);

  const handleClearStorage = React.useCallback(() => {
    onboardingForm.clearStorage();
    toast.success("Stored form data cleared!");
  }, [onboardingForm]);

  // Form recovery and reset operations (memoized)
  const handleResetForm = React.useCallback(() => {
    onboardingForm.resetForm();
    goToStep(1);
    toast.success("Form has been reset to defaults!");
  }, [onboardingForm, goToStep]);

  const handleResetCurrentStep = React.useCallback(() => {
    onboardingForm.resetStep(currentStep as any);
    toast.success(`Step ${currentStep} has been reset!`);
  }, [onboardingForm, currentStep]);

  const handleRecoverFromError = React.useCallback(() => {
    onboardingForm.recoverFromError();
    toast.success("Form data recovered!");
  }, [onboardingForm]);

  const handleCreateBackup = React.useCallback(() => {
    onboardingForm.createBackup();
    toast.success("Backup created successfully!");
  }, [onboardingForm]);

  const handleRestoreFromBackup = React.useCallback(() => {
    const restored = onboardingForm.restoreFromBackup();
    if (restored) {
      toast.success("Form data restored from backup!");
    } else {
      toast.error("No backup found to restore from.");
    }
  }, [onboardingForm]);

  // Handle step completion with animation (memoized)
  const handleStepComplete = React.useCallback(
    (step: number) => {
      setCompletedStepAnimation(step);
      toast.success(`${steps[step - 1]?.title} completed successfully!`);

      setTimeout(() => {
        setCompletedStepAnimation(null);
      }, 2000);
    },
    [steps]
  );

  // Handle step navigation (memoized)
  const handleStepClick = React.useCallback(
    (step: number) => {
      // Only allow navigation to completed steps or current step
      if (
        step <= currentStep ||
        onboardingForm.completedSteps.includes(step as any)
      ) {
        goToStep(step);
      }
    },
    [currentStep, onboardingForm.completedSteps, goToStep]
  );

  // Optimized redirect logic with state management
  React.useEffect(() => {
    // Clear any existing redirect timeout
    if (redirectTimeoutRef.current) {
      clearTimeout(redirectTimeoutRef.current);
    }

    // Don't redirect if already redirecting or still loading
    if (isRedirecting || isUserLoading || isCheckingProvider) {
      return;
    }

    // Redirect if not logged in
    if (!user) {
      setIsRedirecting(true);
      redirectTimeoutRef.current = setTimeout(() => {
        router.push(
          "/login?redirect=" + encodeURIComponent("/onboarding/provider/flow")
        );
      }, 100); // Small delay to prevent flash
      return;
    }

    // Redirect if already a provider
    if (isProvider || isExistingProvider) {
      setIsRedirecting(true);
      redirectTimeoutRef.current = setTimeout(() => {
        router.push("/dashboard");
      }, 100); // Small delay to prevent flash
      return;
    }
  }, [
    user,
    isUserLoading,
    isProvider,
    isExistingProvider,
    isCheckingProvider,
    router,
    isRedirecting,
  ]);

  // Cleanup redirect timeout on unmount
  React.useEffect(() => {
    return () => {
      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
      }
    };
  }, []);

  // Show loading state with skeleton loaders
  if (isUserLoading || isCheckingProvider || isRedirecting) {
    return (
      <div className="min-h-screen bg-background">
        {/* Header Skeleton */}
        <header className="border-b border-border">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="h-6 w-6 bg-muted rounded animate-pulse"></div>
              <div className="h-6 w-32 bg-muted rounded animate-pulse"></div>
            </div>
            <div className="h-9 w-20 bg-muted rounded animate-pulse"></div>
          </div>
        </header>

        {/* Main Content Skeleton */}
        <main className="container mx-auto px-4 py-8">
          <div className="w-full max-w-4xl mx-auto">
            {/* Progress Steps Skeleton */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                {[1, 2, 3].map((step) => (
                  <div
                    key={step}
                    className="flex flex-col items-center space-y-2"
                  >
                    <div className="w-8 h-8 bg-muted rounded-full animate-pulse"></div>
                    <div className="h-4 w-20 bg-muted rounded animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>

            {/* Form Card Skeleton */}
            <LoadingState variant="card" count={1} showFooter={true} />
          </div>
        </main>
      </div>
    );
  }

  // Don't render if user is not logged in (will redirect)
  if (!user) {
    return null;
  }

  // Handle step data changes (memoized for compatibility)
  const handleStepDataChange = React.useCallback(
    (_stepData: Partial<OnboardingFormData>) => {
      // Data changes are now handled automatically by the unified form
      // This function is kept for compatibility with existing step components
    },
    []
  );

  // Handle step validation changes (memoized for compatibility)
  const handleStepValidationChange = React.useCallback(
    (_step: number, _isValid: boolean) => {
      // Validation is now handled automatically by the unified form
      // This function is kept for compatibility with existing step components
    },
    []
  );

  // Handle next step
  const handleNext = () => {
    if (stepValidation[currentStep]) {
      nextStep();
    } else {
      toast.error("Please complete all required fields before continuing.");
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!stepValidation[currentStep]) {
      toast.error("Please complete all required fields.");
      return;
    }

    // Check if user is already a provider (using cached data)
    if (isExistingProvider) {
      toast.error("You are already a catering provider.");
      router.push("/dashboard");
      return;
    }

    // Create backup before submission
    onboardingForm.createBackup();

    // Use TanStack Query mutation with unified form data
    const formData = onboardingForm.getValues();
    createProviderMutation.mutate(formData, {
      onSuccess: () => {
        // Show success message with animation
        setShowSuccessMessage(true);
        toast.success(
          "🎉 Onboarding completed successfully! Welcome to CateringHub!"
        );

        // Clear saved form data on successful submission
        onboardingForm.clearStorage();

        // Redirect after showing success animation
        setIsRedirecting(true);
        redirectTimeoutRef.current = setTimeout(() => {
          router.push("/dashboard");
        }, 2000);
      },
      onError: (error) => {
        console.error("Error submitting onboarding:", error);
        // Error toast is handled by the mutation hook

        // Offer recovery options on error
        toast.error("Submission failed. Your data has been backed up.", {
          action: {
            label: "Recover",
            onClick: () => handleRecoverFromError(),
          },
        });
      },
    });
  };

  // Memoized step content rendering for better performance with Suspense
  const renderStepContent = React.useMemo(() => {
    const formData = onboardingForm.getValues();

    const stepContent = (() => {
      switch (currentStep) {
        case 1:
          return (
            <StepErrorBoundary stepName="Business Information">
              <BusinessInfoStep
                data={formData}
                onDataChange={handleStepDataChange}
                onValidationChange={(isValid) =>
                  handleStepValidationChange(1, isValid)
                }
                form={onboardingForm}
              />
            </StepErrorBoundary>
          );
        case 2:
          return (
            <StepErrorBoundary stepName="Service Details">
              <ServiceDetailsStep
                data={formData}
                onDataChange={handleStepDataChange}
                onValidationChange={(isValid) =>
                  handleStepValidationChange(2, isValid)
                }
                form={onboardingForm}
              />
            </StepErrorBoundary>
          );
        case 3:
          return (
            <StepErrorBoundary stepName="Contact Information">
              <ContactInfoStep
                data={formData}
                onDataChange={handleStepDataChange}
                onValidationChange={(isValid) =>
                  handleStepValidationChange(3, isValid)
                }
                form={onboardingForm}
              />
            </StepErrorBoundary>
          );
        default:
          return null;
      }
    })();

    return (
      <React.Suspense fallback={<LoadingState variant="card" />}>
        {stepContent}
      </React.Suspense>
    );
  }, [
    currentStep,
    onboardingForm,
    handleStepDataChange,
    handleStepValidationChange,
  ]);

  return (
    <OnboardingErrorBoundary maxRetries={3}>
      <div className="min-h-screen bg-background">
        {/* Header */}
        <header className="border-b border-border">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ChefHat className="h-6 w-6" />
              <Typography variant="h5">CateringHub</Typography>
            </div>

            <div className="flex items-center gap-4">
              {/* Storage Status */}
              {storageInfo.hasData && (
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    <Save className="h-3 w-3 mr-1" />
                    Auto-saved
                    {storageInfo.lastSaved && (
                      <span className="ml-1">
                        {new Date(storageInfo.lastSaved).toLocaleTimeString()}
                      </span>
                    )}
                  </Badge>

                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleLoadFromStorage}
                      title="Load saved data"
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCreateBackup}
                      title="Create backup"
                    >
                      <Save className="h-4 w-4" />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" size="sm" title="Reset options">
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle className="flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5 text-warning" />
                            Form Reset Options
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            Choose how you'd like to reset or recover your form
                            data.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <div className="space-y-3">
                          <Button
                            variant="outline"
                            className="w-full justify-start"
                            onClick={() => {
                              handleResetCurrentStep();
                            }}
                          >
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Reset Current Step Only
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full justify-start"
                            onClick={() => {
                              handleRestoreFromBackup();
                            }}
                          >
                            <RotateCcw className="h-4 w-4 mr-2" />
                            Restore from Backup
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full justify-start"
                            onClick={() => {
                              handleRecoverFromError();
                            }}
                          >
                            <AlertTriangle className="h-4 w-4 mr-2" />
                            Auto-Recover Data
                          </Button>
                        </div>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleResetForm}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            Reset Entire Form
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleClearStorage}
                      title="Clear saved data"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              <Button variant="ghost" asChild>
                <Link
                  href="/onboarding/provider"
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back
                </Link>
              </Button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="container mx-auto px-4 py-8">
          {/* Success Message */}
          {showSuccessMessage && (
            <div className="mb-6">
              <SuccessMessage
                message="Onboarding Completed Successfully!"
                description="Welcome to CateringHub! You'll be redirected to your dashboard shortly."
                animate={true}
              />
            </div>
          )}

          {/* Step Completion Animation */}
          {completedStepAnimation && (
            <div className="mb-6">
              <StepCompletionAnimation
                stepNumber={completedStepAnimation}
                stepTitle={steps[completedStepAnimation - 1]?.title || "Step"}
                onComplete={() => setCompletedStepAnimation(null)}
              />
            </div>
          )}

          <MultiStepForm
            steps={steps}
            currentStep={currentStep}
            onNext={handleNext}
            onPrevious={previousStep}
            onSubmit={handleSubmit}
            onStepClick={handleStepClick}
            onStepComplete={handleStepComplete}
            canGoNext={stepValidation[currentStep] || false}
            canGoPrevious={canGoPrevious}
            isSubmitting={createProviderMutation.isPending}
            title="Provider Onboarding"
            description="Complete your catering provider profile to start accepting bookings"
            showProgress={true}
            progressOrientation="horizontal"
            completedSteps={onboardingForm.completedSteps}
            allowStepNavigation={true}
            showSuccessAnimation={true}
          >
            {renderStepContent}
          </MultiStepForm>
        </main>
      </div>
    </OnboardingErrorBoundary>
  );
}
